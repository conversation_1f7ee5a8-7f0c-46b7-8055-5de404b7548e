{% form_theme desk_form 'form/form_theme.html.twig' %}

{% extends 'base.html.twig' %}

{% block title %}Nouveau Bureau{% endblock %}

{% block content %}
    <div class="container mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Créer un nouveau bureau</h1>
                <p class="text-gray-600 dark:text-gray-400">Ajouter un nouveau bureau de travail</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ path('app_space_show', {'id': space.id}) }}"
                   class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors flex items-center font-medium shadow-md">
                    <svg class="w-5 h-5 mr-2" stroke="currentColor" viewBox="0 0 24 24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Retourner vers les bureaux
                </a>
            </div>
        </div>
        <div class="bg-white shadow-md rounded-lg overflow-hidden dark:bg-gray-800 p-6">
            {{ form_start(desk_form) }}
            {{ form_row(desk_form.name) }}
            {{ form_row(desk_form.description) }}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {{ form_row(desk_form.capacity) }}
                {{ form_row(desk_form.pricePerDay) }}
            </div>

            {{ form_row(desk_form.type) }}
            {{ form_row(desk_form.equipments) }}
            <div class="flex justify-end space-x-3">
                <a href="{{ path('app_space_show', {'id': space.id}) }}" class="form-btn-cancel">Annuler</a>
                {{ form_widget(desk_form.save) }}
            </div>
            {{ form_errors(desk_form) }}
            {{ form_end(desk_form) }}
        </div>
    </div>
{% endblock %}