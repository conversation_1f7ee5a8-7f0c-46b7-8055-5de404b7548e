<div id="deskModal" data-modal-closable="true" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 bottom-0 flex items-center justify-center z-50 hidden overflow-y-auto overflow-x-hidden" data-controller="desk-modal">
    <div class="bg-white/50 dark:bg-gray-900/50 absolute inset-0"></div>
    <div class="relative w-11/12 sm:w-3/4 md:w-2/3 lg:w-1/2 max-w-2xl p-4 z-10 max-h-[90vh] overflow-y-auto">
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <div class="flex items-start justify-between p-4 border-b rounded-t dark:border-gray-600">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    Ajouter un bureau a {{ space.name }}
                </h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center" data-modal-hide="deskModal">
                    <svg class="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                </button>
            </div>
            <div class="p-4 space-y-4">
                <div id="desk-form-container">
                    <form action="{{ path('app_desk_new', {'id': space.id}) }}" method="post" class="space-y-4" id="desk-form" data-controller="desk-form">
                        {{ form_widget(desk_form._token) }}
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="md:col-span-2">
                                <div class="mb-2">
                                    {{ form_label(desk_form.name) }}
                                    {{ form_widget(desk_form.name, {
                                        'attr': {
                                            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
                                        }
                                    }) }}
                                    {{ form_errors(desk_form.name) }}
                                </div>
                            </div>

                            <div>
                                <div class="mb-2">
                                    {{ form_label(desk_form.type) }}
                                    {{ form_widget(desk_form.type, {
                                        'attr': {
                                            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
                                        }
                                    }) }}
                                    {{ form_errors(desk_form.type) }}
                                </div>
                            </div>

                            <div>
                                <div class="mb-2">
                                    {{ form_label(desk_form.capacity) }}
                                    {{ form_widget(desk_form.capacity, {
                                        'attr': {
                                            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
                                        }
                                    }) }}
                                    {{ form_errors(desk_form.capacity) }}
                                </div>
                            </div>

                            <div>
                                <div class="mb-2">
                                    <div class="flex items-center">
                                        {{ form_widget(desk_form.isAvailable, {
                                            'attr': {
                                                'class': 'w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800'
                                            }
                                        }) }}
                                        <div class="ml-3 text-sm">
                                            {{ form_label(desk_form.isAvailable, null, {'label_attr': {'class': 'font-light text-gray-500 dark:text-gray-300'}}) }}
                                        </div>
                                    </div>
                                    {{ form_errors(desk_form.isAvailable) }}
                                </div>
                            </div>

                            <div>
                                <div class="mb-2">
                                    {{ form_label(desk_form.pricePerDay) }}
                                    {{ form_widget(desk_form.pricePerDay, {
                                        'attr': {
                                            'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
                                        }
                                    }) }}
                                    {{ form_errors(desk_form.pricePerDay) }}
                                </div>
                            </div>

                            <div class="md:col-span-2">
                                <div class="mb-2">
                                    {{ form_label(desk_form.description) }}
                                    {{ form_widget(desk_form.description, {
                                        'attr': {
                                            'class': 'block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
                                        }
                                    }) }}
                                    {{ form_errors(desk_form.description) }}
                                </div>
                            </div>

                            {{ form_widget(desk_form.equipments, {'attr': {'class': 'hidden'}}) }}
                        </div>
                        <div class="flex justify-end items-center pt-4 pb-3 space-x-3 border-t border-gray-200 rounded-b dark:border-gray-600">
                            <button type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10" data-modal-hide="deskModal">Cancel</button>
                            <button type="submit" class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center cursor-pointer dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800" id="desk-submit-button">Create Desk</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

