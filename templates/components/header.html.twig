<nav class="bg-white border-b border-gray-200 px-4 py-2.5 dark:bg-gray-800 dark:border-gray-700 fixed left-0 right-0 top-0 z-50">
    <div class="flex justify-between items-center max-w-screen-xl mx-auto">
        <div class="flex items-center">
            <button data-drawer-target="drawer-navigation" data-drawer-toggle="drawer-navigation"
                    aria-controls="drawer-navigation"
                    class="p-2 mr-2 text-gray-600 rounded-lg cursor-pointer md:hidden hover:text-gray-900 hover:bg-gray-100 focus:bg-gray-100 dark:focus:bg-gray-700 focus:ring-2 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                <svg aria-hidden="true" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"
                     xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"></path>
                </svg>
                <svg aria-hidden="true" class="hidden w-6 h-6" fill="currentColor" viewBox="0 0 20 20"
                     xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"></path>
                </svg>
                <span class="sr-only">Toggle sidebar</span>
            </button>
            <span class="self-center text-xl sm:text-2xl font-semibold whitespace-nowrap">FlexOffice</span>
        </div>
        <div class="flex items-center lg:order-2">
            <form action="{{ path('app_space_index') }}" method="GET" class="pl-2 lg:block lg:pl-4">
                <label for="topbar-search" class="sr-only">Rechercher espaces</label>
                <div class="relative lg:w-80">
                    <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                        <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                             xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"></path>
                        </svg>
                    </div>
                    <input type="text" name="search" id="topbar-search" placeholder="Rechercher des espaces..."
                           value="{{ app.request.query.get('search', '') }}"
                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:focus:ring-primary-500 dark:focus:border-primary-500"/>
                    <!-- Search results dropdown -->
                    <div id="search-results" class="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg mt-1 hidden z-50 max-h-96 overflow-y-auto">
                        <!-- Results will be populated by JavaScript -->
                    </div>
                </div>
            </form>
            {% if currentUser %}
                <button type="button" id="user-menu-button" aria-expanded="false"
                        data-dropdown-toggle="dropdown-profile"
                        class="flex mx-3 text-sm md:mr-0 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600 rounded-full">
                    <span class="sr-only">Open user menu</span>
                    <div class="relative w-8 h-8 overflow-hidden bg-primary-600 rounded-full">
                        <div class="flex w-full h-full items-center justify-center text-white font-medium text-sm rounded-full">
                            {{ currentUser.firstname|slice(0,1)|upper }}{{ currentUser.lastname|slice(0,1)|upper }}
                        </div>
                    </div>
                </button>
                <div id="dropdown-profile"
                     class="hidden z-50 my-4 w-56 text-base list-none bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600 rounded-xl">
                    <div class="py-3 px-4">
                        <span class="block text-sm font-semibold text-gray-900 dark:text-white">{{ currentUser.firstname }} {{ currentUser.lastname }}</span>
                        <span class="block text-sm text-gray-900 dark:text-gray-300 truncate">{{ currentUser.email }}</span>
                    </div>
                    <ul class="py-1 text-gray-700 dark:text-gray-300" aria-labelledby="dropdown">
                        <li>
                            <a href="{{ path('app_profile_edit') }}"
                               class="block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-400 dark:hover:text-white">
                                Modifier le profil
                            </a>
                        </li>
                        <li>
                            <a href="{{ path('app_logout') }}"
                               class="block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-400 dark:hover:text-white">
                                Se déconnecter
                            </a>
                        </li>
                    </ul>
                </div>
            {% else %}
                <a href="{{ path('app_login') }}"
                   class="block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-400 dark:hover:text-white">
                    {{ 'Login'|trans }}
                </a>
            {% endif %}
        </div>
    </div>
</nav>