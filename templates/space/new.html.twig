{% form_theme space_form 'form/form_theme.html.twig' %}
{% import 'components/day_availabilty_selector.html.twig' as day_availabilty_selector %}

{% extends 'base.html.twig' %}

{% block title %}Nouvel Espace{% endblock %}

{% block content %}
    <div class="container mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Créer un nouvel espace</h1>
                <p class="text-gray-600 dark:text-gray-400">Ajouter un nouveau bureau de travail</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ path('app_space_index') }}"
                   class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors flex items-center font-medium shadow-md">
                    <svg class="w-5 h-5 mr-2" stroke="currentColor" viewBox="0 0 24 24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Retourner vers les espaces
                </a>
            </div>
        </div>
        <div class="bg-white shadow-md rounded-lg overflow-hidden dark:bg-gray-800 p-6">
            {{ form_start(space_form, {'attr': {'class': 'space-y-6', 'data-turbo': true}, 'method': 'POST'}) }}
            {{ form_row(space_form.name) }}
            {{ form_row(space_form.description) }}
            {% include 'address/address_form.html.twig' with {'address': space_form.address} %}
            {{ day_availabilty_selector.render_day_availabilty_selector(space_form.availability, 'Disponibilité de l\'espace') }}
            <div class="flex justify-end space-x-3">
                <a href="{{ path('app_space_index') }}" class="form-btn-cancel">Annuler</a>
                {{ form_widget(space_form.save, {'label': 'Créer espace'}) }}
            </div>
            {{ form_errors(space_form) }}
            {{ form_end(space_form, {'render_rest': false}) }}
        </div>
    </div>
    </div>
{% endblock %}