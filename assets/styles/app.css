@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --bg-color: #ffffff;
    --text-color: #000000;
    --border-color: #d1d5db;
}

html.dark {
    --bg-color: #1f2937;
    --text-color: #f3f4f6;
    --border-color: #4b5563;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

.border {
    border-color: var(--border-color);
}

/* Styles de formulaires unifiés */
.form-container {
    @apply bg-white shadow-md rounded-lg overflow-hidden dark:bg-gray-800 p-6;
}

.form-title {
    @apply text-lg font-semibold mb-3 text-gray-900 dark:text-white;
}

.form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-group {
    @apply mb-4;
}

.form-group-full {
    @apply mb-4 md:col-span-2;
}

.form-label {
    @apply block mb-2 text-sm font-medium text-gray-900 dark:text-white;
}

.form-input, .form-textarea, .form-select {
    @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500;
}

.form-textarea {
    @apply min-h-32;
}

.form-error {
    @apply text-red-500 text-sm mt-1;
}

.form-radio-container {
    @apply flex justify-center space-x-6 py-2;
}

.form-radio-item {
    @apply flex items-center;
}

.form-radio-input {
    @apply w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600;
}

.form-radio-label {
    @apply ml-2 text-sm font-medium text-gray-900 dark:text-gray-300;
}

.form-actions {
    @apply flex justify-end space-x-4 mt-6 md:col-span-2;
}

.form-btn-submit {
    @apply px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800;
}
.form-btn-cancel {
    @apply px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 focus:ring-4 focus:ring-gray-300 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-800;
}

/* Styles spécifiques */
.back-link {
    @apply text-blue-600 hover:underline flex items-center mb-6;
}

.back-link svg {
    @apply w-4 h-4 mr-1;
}

/* Styles Flatpickr */
.flatpickr-calendar {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    padding: 0.5rem;
    width: 325px;
    border: 1px solid #e5e7eb;
    font-family: inherit;
}

.flatpickr-day {
    border-radius: 0.375rem;
    margin: 2px;
    height: 38px;
    line-height: 38px;
    font-size: 0.875rem;
    max-width: 38px;
}

.flatpickr-day:hover {
    background: #f3f4f6;
    border-color: #f3f4f6;
}

.flatpickr-day.selected {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.flatpickr-day.today {
    background: #fef3c7;
    border-color: #fbbf24;
    color: #92400e;
}

#reservation_form_reservationDate {
    display: none !important;
}

/* Dark mode Flatpickr */
.dark .flatpickr-calendar {
    background: #1f2937;
    color: #f3f4f6;
    border: 1px solid #374151;
}

.dark .flatpickr-day {
    color: #f3f4f6;
}

.dark .flatpickr-day:hover {
    background: #374151;
}

/* Notifications Flasher */
.flasher-container {
    position: fixed;
    z-index: 40;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
    max-width: 20rem;
    top: 6rem;
    right: 1rem;
}

.flasher-notification {
    transform: translateX(100%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.flasher-notification.flasher-show {
    transform: translateX(0);
    opacity: 1;
}

@keyframes flasher-progress {
    0% { width: 100%; }
    100% { width: 0; }
}

.flasher-notification-progress {
    animation: flasher-progress linear forwards;
}

/* Day Availability Selector Styles */
.day-selector-checkbox {
    @apply sr-only;
}

.day-selector-label {
    @apply flex flex-col items-center w-full cursor-pointer transition-all duration-200;
}

.day-selector-circle {
    @apply flex items-center justify-center w-12 h-12 mb-1 text-gray-500 bg-white border-2 border-gray-200 rounded-lg dark:border-gray-700 dark:text-gray-400 dark:bg-gray-800 hover:text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:text-gray-300 transition-all duration-200 shadow-sm hover:shadow font-medium text-lg;
}

.day-selector-text {
    @apply text-xs text-gray-500 dark:text-gray-400 transition-colors duration-200;
}

/* Day Availability Selector Styles */
.day-selector-checkbox {
    @apply sr-only;
}

.day-selector-label {
    @apply flex flex-col items-center w-full cursor-pointer transition-all duration-200;
}

.day-selector-circle {
    @apply flex items-center justify-center w-12 h-12 mb-1 text-gray-500 bg-white border-2 border-gray-200 rounded-lg dark:border-gray-700 dark:text-gray-400 dark:bg-gray-800 hover:text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 dark:hover:text-gray-300 transition-all duration-200 shadow-sm hover:shadow font-medium text-lg;
}

.day-selector-text {
    @apply text-xs text-gray-500 dark:text-gray-400 transition-colors duration-200;
}

.day-selector-checkbox:checked + .day-selector-label > .day-selector-circle {
    background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
    color: rgb(30 64 175);
    font-weight: bold;
}

.day-selector-checkbox:checked + .day-selector-label > .day-selector-text {
    color: rgb(30 64 175);
    font-weight: 500;
}

/* Responsive improvements */
@media (max-width: 768px) {
    /* Mobile sidebar overlay */
    #drawer-navigation {
        backdrop-filter: blur(4px);
    }

    /* Mobile header adjustments */
    .navbar-brand {
        font-size: 1.25rem;
    }

    /* Mobile form adjustments */
    .form-grid {
        @apply grid-cols-1;
    }

    /* Mobile card adjustments */
    .card-grid {
        @apply grid-cols-1;
    }

    /* Mobile table responsive */
    .table-responsive {
        @apply overflow-x-auto;
    }

    .table-responsive table {
        @apply min-w-full;
    }

    /* Mobile modal adjustments */
    .modal-content {
        @apply mx-2;
    }
}

@media (max-width: 640px) {
    /* Extra small screens */
    .text-responsive {
        @apply text-sm;
    }

    .padding-responsive {
        @apply p-3;
    }

    .margin-responsive {
        @apply m-2;
    }
}

/* Sidebar mobile backdrop */
.sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 30;
    display: none;
}

.sidebar-backdrop.show {
    display: block;
}

/* Mobile navigation improvements */
@media (max-width: 768px) {
    .mobile-nav-item {
        @apply block w-full text-left px-4 py-3 text-sm;
    }

    .mobile-nav-item:hover {
        @apply bg-gray-100 dark:bg-gray-700;
    }
}

/* Responsive utilities */
.hide-mobile {
    @apply hidden md:block;
}

.show-mobile {
    @apply block md:hidden;
}

.responsive-text {
    @apply text-sm sm:text-base lg:text-lg;
}

.responsive-padding {
    @apply p-2 sm:p-4 lg:p-6;
}

.responsive-margin {
    @apply m-2 sm:m-4 lg:m-6;
}

/* Search functionality styles */
#search-results {
    max-height: 400px;
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;
}

#search-results::-webkit-scrollbar {
    width: 6px;
}

#search-results::-webkit-scrollbar-track {
    background: #f9fafb;
    border-radius: 3px;
}

#search-results::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

#search-results::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

.dark #search-results {
    scrollbar-color: #4b5563 #1f2937;
}

.dark #search-results::-webkit-scrollbar-track {
    background: #1f2937;
}

.dark #search-results::-webkit-scrollbar-thumb {
    background: #4b5563;
}

.dark #search-results::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Search input focus styles */
#topbar-search:focus + #search-results,
#sidebar-search:focus + #search-results {
    display: block;
}

/* Search result item hover animation */
#search-results a {
    transition: all 0.2s ease-in-out;
}

#search-results a:hover {
    transform: translateX(2px);
}

/* Search highlight animation */
@keyframes searchHighlight {
    0% { background-color: transparent; }
    50% { background-color: rgba(59, 130, 246, 0.1); }
    100% { background-color: transparent; }
}

.search-highlight {
    animation: searchHighlight 0.6s ease-in-out;
}
